#!/usr/bin/env python3
"""
Simple test script for MCP Search Server
Tests basic functionality without external dependencies
"""

import asyncio
import json
import subprocess
import sys
import time
import os


async def test_server_basic():
    """Test basic server functionality"""
    print("🧪 Testing MCP Search Server...")
    
    # Test 1: Server starts without crashing
    print("1. Testing server startup...")
    process = subprocess.Popen(
        [sys.executable, "mcp_server.py"],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    # Give it time to start
    await asyncio.sleep(2)
    
    if process.poll() is not None:
        stdout, stderr = process.communicate()
        print(f"❌ Server crashed on startup!")
        print(f"STDOUT: {stdout}")
        print(f"STDERR: {stderr}")
        return False
    
    print("✅ Server started successfully")
    
    # Test 2: Server responds to initialize
    print("2. Testing MCP initialize...")
    init_message = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        }
    }
    
    try:
        # Send initialize message
        process.stdin.write(json.dumps(init_message) + "\n")
        process.stdin.flush()
        
        # Wait for response
        await asyncio.sleep(1)
        
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"❌ Server crashed after initialize!")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return False
        
        print("✅ Server handled initialize message")
        
        # Test 3: List tools
        print("3. Testing tools list...")
        list_tools_message = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list",
            "params": {}
        }
        
        process.stdin.write(json.dumps(list_tools_message) + "\n")
        process.stdin.flush()
        
        await asyncio.sleep(1)
        
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"❌ Server crashed after tools/list!")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return False
        
        print("✅ Server handled tools/list message")
        
    finally:
        # Clean up
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
            process.wait()
    
    print("🎉 All basic tests passed!")
    return True


async def test_with_mock_client():
    """Test with a simple mock MCP client"""
    print("\n🔧 Testing with mock MCP client...")
    
    # Start server
    process = subprocess.Popen(
        [sys.executable, "mcp_server.py"],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    try:
        await asyncio.sleep(1)
        
        # Initialize
        init_msg = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {"name": "test", "version": "1.0"}
            }
        }
        
        process.stdin.write(json.dumps(init_msg) + "\n")
        process.stdin.flush()
        
        # Read response
        response_line = process.stdout.readline()
        if response_line:
            try:
                response = json.loads(response_line)
                print(f"✅ Initialize response: {response.get('result', {}).get('serverInfo', {}).get('name', 'Unknown')}")
            except json.JSONDecodeError:
                print(f"⚠️  Non-JSON response: {response_line.strip()}")
        
        # List tools
        tools_msg = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list",
            "params": {}
        }
        
        process.stdin.write(json.dumps(tools_msg) + "\n")
        process.stdin.flush()
        
        # Read tools response
        response_line = process.stdout.readline()
        if response_line:
            try:
                response = json.loads(response_line)
                tools = response.get('result', {}).get('tools', [])
                print(f"✅ Found {len(tools)} tools: {[t.get('name') for t in tools]}")
            except json.JSONDecodeError:
                print(f"⚠️  Non-JSON tools response: {response_line.strip()}")
        
    except Exception as e:
        print(f"❌ Error during mock client test: {e}")
        return False
    
    finally:
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
            process.wait()
    
    return True


def check_dependencies():
    """Check if required dependencies are available"""
    print("🔍 Checking dependencies...")
    
    try:
        import mcp
        print("✅ MCP SDK available")
    except ImportError:
        print("❌ MCP SDK not available - install with: pip install mcp")
        return False
    
    try:
        import selenium
        print("✅ Selenium available")
    except ImportError:
        print("❌ Selenium not available - install with: pip install selenium")
        return False
    
    try:
        import dotenv
        print("✅ python-dotenv available")
    except ImportError:
        print("❌ python-dotenv not available - install with: pip install python-dotenv")
        return False
    
    return True


async def main():
    """Main test function"""
    print("🚀 MCP Search Server Test Suite")
    print("=" * 40)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Missing dependencies. Please install them first.")
        return
    
    # Check if server file exists
    if not os.path.exists("mcp_server.py"):
        print("❌ mcp_server.py not found in current directory")
        return
    
    # Run basic tests
    success = await test_server_basic()
    if not success:
        print("\n❌ Basic tests failed")
        return
    
    # Run mock client tests
    success = await test_with_mock_client()
    if not success:
        print("\n❌ Mock client tests failed")
        return
    
    print("\n🎉 All tests completed successfully!")
    print("\n📝 Next steps:")
    print("   1. Test with a real MCP client (like Claude Desktop)")
    print("   2. Try the search_web tool with different queries")
    print("   3. Test the browse_url tool with various websites")


if __name__ == "__main__":
    asyncio.run(main())
