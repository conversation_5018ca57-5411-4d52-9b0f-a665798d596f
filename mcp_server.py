#!/usr/bin/env python3
"""
Search MCP Server
Provides web search capabilities through MCP protocol using stdio
"""

import asyncio
import os
from typing import Optional
from dotenv import load_dotenv
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service as ChromeService
from contextlib import asynccontextmanager
from typing import AsyncIterator
from dataclasses import dataclass

# Import official MCP SDK
from mcp.server.fastmcp import FastMCP

load_dotenv()

# Configuration
CUSTOM_CHROME = os.getenv('CUSTOM_CHROME')
CUSTOM_CHROME_DRIVER = os.getenv('CUSTOM_CHROME_DRIVER')

@dataclass
class AppContext:
    browser: webdriver.Chrome

@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[AppContext]:
    """Manage application lifecycle with browser setup and cleanup"""
    # Initialize browser on startup
    chrome_options = Options()
    chrome_options.add_argument('--headless=new')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--window-size=1280,1024')
    chrome_options.add_argument('--disable-extensions')

    if CUSTOM_CHROME:
        chrome_options.binary_location = CUSTOM_CHROME

    if CUSTOM_CHROME_DRIVER:
        service = ChromeService(executable_path=CUSTOM_CHROME_DRIVER)
    else:
        service = ChromeService()

    browser = webdriver.Chrome(service=service, options=chrome_options)

    try:
        yield AppContext(browser=browser)
    finally:
        # Cleanup on shutdown
        browser.quit()

# Create MCP server with lifespan management
mcp = FastMCP("Search Server", lifespan=app_lifespan)

@mcp.tool()
async def search_web(query: str, engine: str = "bing") -> str:
    """
    Search the web using the specified search engine.

    Args:
        query: The search query to execute
        engine: The search engine to use (bing or google)

    Returns:
        Search results including page title and content
    """
    if not query:
        return "Error: No query provided"

    try:
        # Get browser from lifespan context
        ctx = mcp.get_context()
        browser = ctx.request_context.lifespan_context.browser

        # Perform search based on engine
        if engine.lower() == "bing":
            browser.get(f"https://www.bing.com/search?q={query}")
        elif engine.lower() == "google":
            browser.get(f"https://www.google.com/search?q={query}")
        else:
            return f"Error: Unsupported search engine: {engine}"

        # Wait for page to load
        await asyncio.sleep(2)

        # Extract search results
        page_source = browser.page_source
        title = browser.title

        return f"Title: {title}\nQuery: {query}\nEngine: {engine}\nContent: {page_source[:1000]}..."

    except Exception as e:
        return f"Error: {str(e)}"

@mcp.tool()
async def browse_url(url: str) -> str:
    """
    Navigate to a specific URL and extract content

    Args:
        url: The URL to navigate to

    Returns:
        Page content or error message
    """
    if not url:
        return "Error: No URL provided"

    try:
        # Get browser from lifespan context
        ctx = mcp.get_context()
        browser = ctx.request_context.lifespan_context.browser

        # Navigate to URL
        browser.get(url)

        # Wait for page to load
        await asyncio.sleep(2)

        # Extract page content
        page_source = browser.page_source
        title = browser.title
        current_url = browser.current_url

        return f"Title: {title}\nURL: {current_url}\nContent: {page_source[:1000]}..."

    except Exception as e:
        return f"Error: {str(e)}"

@mcp.resource("search://results/{query}")
def get_search_results(query: str) -> str:
    """Get cached search results for a query"""
    return f"Search results resource for query: {query}"

@mcp.prompt()
def search_prompt(query: str) -> str:
    """Create a search prompt template"""
    return f"Please search for information about: {query}"

if __name__ == "__main__":
    print("Starting MCP Search Server with stdio protocol...")
    mcp.run()
