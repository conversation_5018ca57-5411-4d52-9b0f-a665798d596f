#!/usr/bin/env python3
"""
Browser Automation MCP Server
Provides browser automation capabilities through MCP protocol using fastmcp
"""

import asyncio
import os
import json
from typing import Any, Dict, List, Optional
from dotenv import load_dotenv
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service as ChromeService
from browser_use import Agent, Browser
from browser_use.controller.service import Controller
from browser_use.controller.registry.service import Registry
from browser_use.controller.views import (
    ClickElementAction,
    DoneAction,
    ExtractPageContentAction,
    GoToUrlAction,
    InputTextAction,
    OpenTabAction,
    ScrollDownAction,
    SearchGoogleAction,
    SwitchTabAction,
)
from Screenshot import Screenshot
from langchain_openai import ChatOpenAI

# Import fastmcp
from fastmcp import FastMCP

load_dotenv()

# Configuration
API_KEY = os.getenv('API_KEY')
BASE_URL = os.getenv('Base_URL')
MODEL = os.getenv('Model')
CUSTOM_CHROME = os.getenv('CUSTOM_CHROME')
CUSTOM_CHROME_DRIVER = os.getenv('CUSTOM_CHROME_DRIVER')

class RVBrowser(Browser):
    def __init__(self, headless: bool = True, keep_open: bool = False):
        self.headless = headless
        self.keep_open = keep_open
        self.MINIMUM_WAIT_TIME = 0.5
        self.MAXIMUM_WAIT_TIME = 5
        self._tab_cache: dict[str, Any] = {}
        self._current_handle = None
        self._ob = Screenshot.Screenshot()
        # Initialize driver during construction
        self.driver: webdriver.Chrome | None = self._setup_webdriver()
        self._cached_state = Browser._update_state(self)
    
    def _setup_webdriver(self) -> webdriver.Chrome:
        """Sets up and returns a Selenium WebDriver instance with anti-detection measures."""
        try:
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument('--headless=new')

            # Essential automation and performance settings
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--window-size=1280,1024')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-popup-blocking')
            chrome_options.add_argument('--disable-infobars')
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')
            chrome_options.add_argument('--disable-renderer-backgrounding')

            if CUSTOM_CHROME:
                chrome_options.binary_location = CUSTOM_CHROME
            
            if CUSTOM_CHROME_DRIVER:
                service = ChromeService(executable_path=CUSTOM_CHROME_DRIVER)
            else:
                service = ChromeService()
            
            driver = webdriver.Chrome(service=service, options=chrome_options)

            # Execute stealth scripts
            driver.execute_cdp_cmd(
                'Page.addScriptToEvaluateOnNewDocument',
                {
                    'source': """
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined
                    });
                    
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['en-US', 'en']
                    });
                    
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5]
                    });
                    
                    window.chrome = {
                        runtime: {}
                    };
                    
                    Object.defineProperty(navigator, 'permissions', {
                        get: () => ({
                            query: Promise.resolve.bind(Promise)
                        })
                    });
                """
                },
            )
            return driver

        except Exception as e:
            if hasattr(self, 'driver') and self.driver:
                try:
                    self.driver.quit()
                    self.driver = None
                except Exception:
                    pass
            raise

class RVController(Controller):
    def __init__(self, keep_open: bool = False):
        self.browser = RVBrowser(keep_open=keep_open)
        self.registry = Registry()
        self._register_default_actions()
        self._register_bing()
    
    def _register_bing(self):
        """Register Bing search action"""
        @self.registry.action(
            'Search Bing', param_model=SearchGoogleAction, requires_browser=True
        )
        def search_bing(params: SearchGoogleAction, browser: Browser):
            driver = browser._get_driver()
            driver.get(f'https://www.bing.com/search?q={params.query}')
            browser.wait_for_page_load()

# Global browser controller instance
browser_controller: Optional[RVController] = None
llm: Optional[ChatOpenAI] = None

def initialize_browser():
    """Initialize the browser controller and LLM"""
    global browser_controller, llm
    if browser_controller is None:
        browser_controller = RVController(keep_open=True)
    if llm is None:
        llm = ChatOpenAI(model=MODEL, api_key=API_KEY, base_url=BASE_URL)

def cleanup_browser():
    """Cleanup browser resources"""
    global browser_controller
    if browser_controller and browser_controller.browser.driver:
        try:
            browser_controller.browser.driver.quit()
        except Exception:
            pass
        browser_controller = None

# Initialize FastMCP server
mcp = FastMCP("Browser Automation Server")

@mcp.tool()
async def search_bing(query: str) -> str:
    """
    Search Bing for the given query
    
    Args:
        query: The search query string
    
    Returns:
        Search results or error message
    """
    try:
        initialize_browser()
        
        agent = Agent(
            task=f"Search Bing for: {query}",
            llm=llm,
            controller=browser_controller,
            use_vision=False,
        )
        
        result = await agent.run()
        return str(result)
    
    except Exception as e:
        return f"Error performing Bing search: {str(e)}"

@mcp.tool()
async def browse_url(url: str) -> str:
    """
    Navigate to a specific URL and extract content
    
    Args:
        url: The URL to navigate to
    
    Returns:
        Page content or error message
    """
    try:
        initialize_browser()
        
        agent = Agent(
            task=f"Go to {url} and extract the main content",
            llm=llm,
            controller=browser_controller,
            use_vision=False,
        )
        
        result = await agent.run()
        return str(result)
    
    except Exception as e:
        return f"Error browsing URL: {str(e)}"

@mcp.tool()
async def automated_task(task_description: str, use_vision: bool = False) -> str:
    """
    Perform an automated browser task based on natural language description
    
    Args:
        task_description: Natural language description of the task to perform
        use_vision: Whether to use vision capabilities for the task
    
    Returns:
        Task result or error message
    """
    try:
        initialize_browser()
        
        agent = Agent(
            task=task_description,
            llm=llm,
            controller=browser_controller,
            use_vision=use_vision,
        )
        
        result = await agent.run()
        return str(result)
    
    except Exception as e:
        return f"Error performing automated task: {str(e)}"

@mcp.tool()
async def get_page_content() -> str:
    """
    Extract content from the current page
    
    Returns:
        Current page content or error message
    """
    try:
        initialize_browser()
        
        if browser_controller.browser.driver:
            # Get current page title and URL
            title = browser_controller.browser.driver.title
            url = browser_controller.browser.driver.current_url
            
            # Extract page text content
            page_source = browser_controller.browser.driver.page_source
            
            return f"Page Title: {title}\nURL: {url}\nContent preview: {page_source[:1000]}..."
        else:
            return "No browser session active"
    
    except Exception as e:
        return f"Error getting page content: {str(e)}"

@mcp.tool()
async def close_browser() -> str:
    """
    Close the browser and cleanup resources
    
    Returns:
        Confirmation message
    """
    try:
        cleanup_browser()
        return "Browser closed successfully"
    
    except Exception as e:
        return f"Error closing browser: {str(e)}"

@mcp.tool()
async def search_chinese_scenic_spots() -> str:
    """
    Search for the most beautiful scenic spots in China from south to north
    
    Returns:
        List of scenic spots or error message
    """
    try:
        initialize_browser()
        
        agent = Agent(
            task="使用 Bing 搜索中国最美的十个景点，按从南到北输出",
            llm=llm,
            controller=browser_controller,
            use_vision=False,
        )
        
        result = await agent.run()
        return str(result)
    
    except Exception as e:
        return f"Error searching Chinese scenic spots: {str(e)}"

if __name__ == "__main__":
    # Add cleanup on exit
    import atexit
    atexit.register(cleanup_browser)
    
    # Run the MCP server
    mcp.run()
    